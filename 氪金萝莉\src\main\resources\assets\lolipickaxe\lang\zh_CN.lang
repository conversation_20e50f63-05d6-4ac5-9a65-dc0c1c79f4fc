﻿
itemGroup.loli=§c氪金萝莉
itemGroup.loliRecipe=§c氪金萝莉-合成材料

item.loliPickaxe.name=§c氪金萝莉
item.smallLoliPickaxe.name=§c普通萝莉
item.loliDispersal.name=§c萝莉退散!
item.bugEntityClear.name=§c清除只在客户端存在的生物(擦屁股)
item.loliCoalAddon.name=回到过去升级
item.loliIronAddon.name=采集速度升级
item.loliGoldAddon.name=攻击伤害升级
item.loliRedstoneAddon.name=攻击速度升级
item.loliLapisAddon.name=时运掠夺升级
item.loliDiamondAddon.name=采集等级升级
item.loliEmeraldAddon.name=采集范围升级
item.loliObsidianAddon.name=勇气之霎升级
item.loliGlowAddon.name=状态效果升级
item.loliQuartzAddon.name=攻击范围升级
item.loliNetherStarAddon.name=储存容量升级
item.loliAutoFurnaceAddon.name=自动熔炼升级
item.loliFlyAddon.name=飞行升级
item.loliEntitySoulAddon.name=生物灵魂
item.magicKey.name=魔法钥匙

item.loliMaterialFormat=%2$s%1$s
item.loliMaterial.0=一级
item.loliMaterial.1=二级
item.loliMaterial.2=三级
item.loliMaterial.3=四级
item.loliMaterial.4=五级
item.loliMaterial.5=六级
item.loliMaterial.6=七级
item.loliMaterial.7=八级
item.loliMaterial.8=九级
item.loliMaterial.9=十级
item.loliMaterial.end=终极
item.loliMaterial.recipe=九个%1$s升级合成一个%2$s升级,共%3$s

enchantment.level.16=XVI
enchantment.level.32=XXXII

smallLoliPickaxe.LoliDodge=%1$f 回到过去
smallLoliPickaxe.LoliDiggingSpeed=%1$d 采集速度
smallLoliPickaxe.LoliAttackDamage=%1$f 攻击伤害
smallLoliPickaxe.LoliAttackSpeed=%1$d 攻击速度
smallLoliPickaxe.LoliFortuneLevel=%1$d 时运掠夺
smallLoliPickaxe.LoliDiggingLevel=%1$d 采集等级
smallLoliPickaxe.LoliDiggingRange=%1$d*%1$d*%1$d 采集范围
smallLoliPickaxe.LoliAntiInjury=%1$f 勇气之霎
smallLoliPickaxe.LoliBuff=%1$d 状态效果
smallLoliPickaxe.LoliHitRange=%1$d*%1$d*%1$d 范围攻击
smallLoliPickaxe.LoliBackpackPage=%1$d 背包容量
smallLoliPickaxe.LoliAutoFurnace=自动熔炼
smallLoliPickaxe.LoliFly=飞行

container.loliPickaxe=§c氪金萝莉储藏室
container.loliPickaxe.stackCount=§a物品数量§b %1$d
container.smallLoliPickaxe=普通萝莉储藏室

gui.loliCardOnline=图片URL
gui.loliAdd=添加
gui.loliRemove=移除
gui.loliEnch=附魔
gui.loliPotion=药水
gui.loliAbsolute=绝对坐标
gui.loliRelative=相对坐标
gui.password=密码

item.loliRecord.name=萝莉唱片
item.record.lolirecord.desc=ばか変態煩い(av6752335)

item.loliCard.name=萝莉卡片
item.loliCardAlbum.name=萝莉卡片册
item.loliCardOnline.name=萝莉网络卡片

tile.LoliBlueScreenTNT.name=§c蓝屏炸弹
tile.LoliExitTNT.name=§c蹦溃炸弹
tile.LoliFailRespondTNT.name=§c未响应炸弹
tile.LoliAltar.name=萝莉祭坛
tile.PasswordWorkBench.name=密码工作台

loliAltar.use=按指定格式摆放祭坛后使用普通萝莉或氪金萝莉右键位于中心的祭坛即可召唤萝莉

death.attack.loli=%1$s被%2$s用氪金萝莉抹杀了

loliPickaxe.range=采掘范围更改为%1$d*%1$d*%1$d
loliPickaxe.killrangeentity=攻击了周围%1$d*%1$d范围内的%2$d个生物
loliPickaxe.curRange=采掘范围 %1$d*%1$d*%1$d
loliPickaxe.mandatoryDrop=强制掉落
loliPickaxe.stopOnLiquid=显示流体边框
loliPickaxe.blockReachDistance=采掘距离 %1$f
loliPickaxe.autoAccept=自动收纳
loliPickaxe.thorns=反伤
loliPickaxe.killRange=潜行右键范围攻击 %1$d*%1$d*%1$d
loliPickaxe.autoKillRange=自动范围攻击 %1$d*%1$d*%1$d
loliPickaxe.compulsoryRemove=强制清除
loliPickaxe.validToAmityEntity=友军杀手
loliPickaxe.validToAllEntity=实体杀手
loliPickaxe.clearInventory=清空背包
loliPickaxe.dropItems=强制掉落
loliPickaxe.kickPlayer=掉线打击
loliPickaxe.reincarnation=伊邪那美
loliPickaxe.beyondRedemption=灵魂超度
loliPickaxe.blueScreenAttack=蓝屏打击
loliPickaxe.exitAttack=蹦溃打击
loliPickaxe.failRespondAttack=未响应打击
loliPickaxe.killFacing=范围攻击 %1$d,%2$f
loliPickaxe.infiniteBattery=超级电池
loliPickaxe.speed=TREE(3)
loliPickaxe.damage=TREE(3)

buffAttackTNT.enable=已启用特效攻击
buffAttackTNT.disable=未启用特效攻击

enchantment.LoliAutoFurnace=自动熔炼

commands.loli.usage=/loli <flag> [value]
commands.loli.notfound=未知的配置
commands.loli.errortype=类型错误
commands.loli.set=将%1$s(§d%2$s)的值设置成了%3$s
commands.loli.get=%1$s(§d%2$s)的值为%3$s
commands.loli.list=%1$s(§d%2$s)
commands.loli.reload=已重新加载配置文件
commands.loliattack.usage=/loliattack <玩家> <攻击特效>
commands.loliattack.notfound=未知的攻击特效
commands.loliattack.disable=未启用特效攻击指令
commands.page.button.pre=上一页
commands.page.button.next=下一页

config.worning.int=§e警告§f: %1$s 的值为 %2$d
config.worning.double=§e警告§f: %1$s 的值为 %2$f
config.worning.boolean.enable=§e警告§f: §a已开启§f %1$s 功能
config.worning.boolean.disable=§e警告§f: §c已关闭§f %1$s 功能
config.worning.string=§e警告§f: %1$s 的值为 %2$s
config.worning.list=§e警告§f: %1$s 的值为
config.worning.list.element=  %1$s
config.worning.map=§e警告§f: %1$s 的值为
config.worning.map.element=  %1$s
config.worning.guiChangeList=§e警告§f: 以下功能可由持有氪金萝莉的玩家自由配置
config.worning.guiChangeList.element=  %1$s

entity.Loli.name=萝莉
entity.LoliBuffAttackTNT.name=§c特效炸弹

key.category.lolipickaxe=§c氪金萝莉
key.lolipickaxe.loli_config=配置§c氪金萝莉
key.lolipickaxe.loli_enchantment=附魔§c氪金萝莉
key.lolipickaxe.loli_potion=药水§c氪金萝莉
key.lolipickaxe.loli_space_folding=跨世界§c氪金萝莉
key.lolipickaxe.loli_container=打开§c氪金萝莉储藏室
key.lolipickaxe.loli_container_blacklist=打开§c氪金萝莉黑名单