package com.anotherstar.client.util.obj;

import java.nio.ByteBuffer;

import it.unimi.dsi.fastutil.ints.Int2ObjectArrayMap;
import net.minecraft.client.renderer.BufferBuilder;
import net.minecraft.client.renderer.Tessellator;
import net.minecraft.client.renderer.vertex.DefaultVertexFormats;
import net.minecraft.util.math.Vec3d;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

public class Face {

	public static int defaultColor = 0xFFFFFFFF;

	public static void setColor(int color) {
		defaultColor = color;
	}

	public static void resetColor() {
		defaultColor = 0xFFFFFFFF;
	}

	public Vertex[] vertices;
	public Vertex[] vertexNormals;
	public Vertex faceNormal;
	public TextureCoordinate[] textureCoordinates;
	public int glDrawingMode;
	public String usemtl;

	private Int2ObjectArrayMap<ByteBuffer> cache = new Int2ObjectArrayMap<>();

	@SideOnly(Side.CLIENT)
	public void render(Tessellator tessellator) {
		if (faceNormal == null) {
			faceNormal = this.calculateFaceNormal();
		}
		render(tessellator, 0.0005F);
	}

	@SideOnly(Side.CLIENT)
	public void render(Tessellator tessellator, float textureOffset) {
		BufferBuilder wr = tessellator.getBuffer();
		ByteBuffer cached = this.cache.get(Face.defaultColor);
		if (cached != null) {
			cached.position(0);
			wr.begin(glDrawingMode, DefaultVertexFormats.POSITION_TEX_COLOR_NORMAL);
			wr.putBulkData(cached);
			tessellator.draw();
			return;
		}

		wr = new BufferBuilder(this.vertices.length * wr.getVertexFormat().getNextOffset());
		wr.begin(glDrawingMode, DefaultVertexFormats.POSITION_TEX_COLOR_NORMAL);

		float averageU = 0F;
		float averageV = 0F;
		int textureCoordinates_length = textureCoordinates.length;
		if ((textureCoordinates != null) && (textureCoordinates_length > 0)) {
			for (int i = 0; i < textureCoordinates_length; ++i) {
				averageU += textureCoordinates[i].u;
				averageV += textureCoordinates[i].v;
			}

			averageU = averageU / textureCoordinates_length;
			averageV = averageV / textureCoordinates_length;
		}

		int r = defaultColor >> 16 & 255;
		int g = defaultColor >> 8 & 255;
		int b = defaultColor & 255;
		int a = defaultColor >> 24 & 255;

		float offsetU, offsetV;
		Vertex normal;
		for (int i = 0; i < vertices.length; ++i) {
			wr.pos(vertices[i].x, vertices[i].y, vertices[i].z);

			if ((textureCoordinates != null) && (textureCoordinates_length > 0)) {
				offsetU = textureOffset;
				offsetV = textureOffset;

				if (textureCoordinates[i].u > averageU) {
					offsetU = -offsetU;
				}
				if (textureCoordinates[i].v > averageV) {
					offsetV = -offsetV;
				}

				wr.tex(textureCoordinates[i].u + offsetU, textureCoordinates[i].v + offsetV);
			} else {
				wr.tex(0, 0);
			}
			wr.color(r, g, b, a);

			if (vertexNormals != null) {
				normal = vertexNormals[i];
				wr.normal(normal.x * -1.05f, normal.y * -1.05f, normal.z * -1.05f);
			} else {
				wr.normal(faceNormal.x, faceNormal.y, faceNormal.z);
			}
			wr.endVertex();
		}
		wr.finishDrawing();
		this.cache.put(Face.defaultColor, wr.getByteBuffer().asReadOnlyBuffer());
	}

	public Vertex calculateFaceNormal() {
		Vec3d v1 = new Vec3d(vertices[1].x - vertices[0].x, vertices[1].y - vertices[0].y, vertices[1].z - vertices[0].z);
		Vec3d v2 = new Vec3d(vertices[2].x - vertices[0].x, vertices[2].y - vertices[0].y, vertices[2].z - vertices[0].z);
		Vec3d normalVector = v1.crossProduct(v2).normalize();

		return new Vertex((float) normalVector.x, (float) normalVector.y, (float) normalVector.z);
	}

}
