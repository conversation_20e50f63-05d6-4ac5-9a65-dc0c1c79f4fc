package com.example.modid;

import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.item.ItemStack;
import net.minecraft.util.text.TextComponentString;
import net.minecraft.util.text.TextFormatting;
import net.minecraftforge.event.entity.living.LivingAttackEvent;
import net.minecraftforge.event.entity.living.LivingDamageEvent;
import net.minecraftforge.event.entity.living.LivingDeathEvent;
import net.minecraftforge.event.entity.living.LivingEvent.LivingUpdateEvent;
import net.minecraftforge.event.entity.living.LivingHurtEvent;
import net.minecraftforge.event.entity.player.AttackEntityEvent;
import net.minecraftforge.fml.common.eventhandler.EventPriority;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;

public class EventHandler {

    // 拦截攻击事件（最早的伤害事件）
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerAttack(LivingAttackEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (hasTianshiSword(player)) {
                event.setCanceled(true);
                // 可选：显示无敌提示
                 
                player.sendMessage(new TextComponentString(TextFormatting.GOLD + "天屎剑护体！无敌状态！"));
                
            }
        }
    }

    // 拦截伤害计算事件
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerHurt(LivingHurtEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (hasTianshiSword(player)) {
                event.setCanceled(true);
            }
        }
    }

    // 拦截最终伤害事件
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onPlayerDamage(LivingDamageEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (hasTianshiSword(player)) {
                event.setCanceled(true);
            }
        }
    }

    // 防止setHealth等方法造成的伤害
    @SubscribeEvent
    public void onLivingUpdate(LivingUpdateEvent event) {
        if (event.getEntityLiving() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntityLiving();
            if (hasTianshiSword(player)) {
                player.setHealth(player.getMaxHealth());
            }
        }
    }

    // 防止onDeath方法造成的死亡
    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onLivingDeath(LivingDeathEvent event) {
        if (event.getEntity() instanceof EntityPlayer) {
            EntityPlayer player = (EntityPlayer) event.getEntity();
            if (hasTianshiSword(player)) {
                event.setCanceled(true);
                player.setHealth(player.getMaxHealth());
                player.sendMessage(new TextComponentString(TextFormatting.RED + "凡人，休想杀死神！"));
            }
        }
    }

    @SubscribeEvent(priority = EventPriority.HIGHEST)
    public void onAttack(AttackEntityEvent event) {
        if (event.getTarget() instanceof EntityPlayer && event.getEntityPlayer() != null) {
            EntityPlayer target = (EntityPlayer) event.getTarget();
            EntityPlayer attacker = event.getEntityPlayer();
            if (hasTianshiSword(target) && isHoldingLoliPickaxe(attacker)) {
                event.setCanceled(true);
                target.sendMessage(new TextComponentString(TextFormatting.GOLD + "天屎剑成功抵挡了氪金萝莉的攻击！"));
                attacker.sendMessage(new TextComponentString(TextFormatting.RED + "你的攻击被天屎剑无效化了！"));
            }
        }
    }

    private boolean hasTianshiSword(EntityPlayer player) {
        // 检查主手、副手和背包所有物品栏
        for (ItemStack stack : player.inventory.mainInventory) {
            if (!stack.isEmpty() && stack.getItem() instanceof TianshiSword) {
                return true;
            }
        }
        for (ItemStack stack : player.inventory.offHandInventory) {
            if (!stack.isEmpty() && stack.getItem() instanceof TianshiSword) {
                return true;
            }
        }
        return false;
    }

    private boolean isHoldingLoliPickaxe(EntityPlayer player) {
        ItemStack mainHandStack = player.getHeldItemMainhand();
        ItemStack offHandStack = player.getHeldItemOffhand();
        String loliPickaxeName = "lolipickaxe:loli_pickaxe";
        return (!mainHandStack.isEmpty() && mainHandStack.getItem().getRegistryName().toString().equals(loliPickaxeName)) ||
               (!offHandStack.isEmpty() && offHandStack.getItem().getRegistryName().toString().equals(loliPickaxeName));
    }
}
