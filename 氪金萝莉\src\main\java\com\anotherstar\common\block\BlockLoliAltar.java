package com.anotherstar.common.block;

import java.util.List;

import com.anotherstar.client.creative.CreativeTabLoader;
import com.anotherstar.common.entity.EntityLoli;
import com.anotherstar.common.item.tool.ILoli;
import com.anotherstar.common.item.tool.ItemSmallLoliPickaxe;

import net.minecraft.block.Block;
import net.minecraft.block.material.Material;
import net.minecraft.block.state.IBlockState;
import net.minecraft.client.resources.I18n;
import net.minecraft.client.util.ITooltipFlag;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.Blocks;
import net.minecraft.item.ItemStack;
import net.minecraft.util.EnumFacing;
import net.minecraft.util.EnumHand;
import net.minecraft.util.math.BlockPos;
import net.minecraft.world.World;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

public class BlockLoliAltar extends Block {

	private Block[][] altarStructure;

	public BlockLoliAltar() {
		super(Material.IRON);
		this.setCreativeTab(CreativeTabLoader.loliTabs);
		this.setUnlocalizedName("LoliAltar");
	}

	private void initAltarStructure() {
		altarStructure = new Block[][] { { BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR },
			{ BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar },
			{ BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar },
			{ BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar },
			{ Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR },
			{ BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar },
			{ BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar },
			{ BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar },
			{ Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR },
			{ BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar },
			{ BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar },
			{ BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar },
			{ Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR }, { BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, Blocks.AIR, Blocks.AIR, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar }, { BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar, Blocks.AIR, BlockLoader.loliAltar, BlockLoader.loliAltar, BlockLoader.loliAltar } };
	}

	@Override
	public boolean onBlockActivated(World worldIn, BlockPos pos, IBlockState state, EntityPlayer playerIn, EnumHand hand, EnumFacing facing, float hitX, float hitY, float hitZ) {
		if (!worldIn.isRemote) {
			if (altarStructure == null) {
				initAltarStructure();
			}
			ItemStack stack = playerIn.getHeldItem(hand);
			if (stack.isEmpty()) {
				if (playerIn.isCreative() && playerIn.isSneaking()) {
					for (int dx = -31; dx <= 31; dx++) {
						for (int dz = -31; dz <= 31; dz++) {
							worldIn.setBlockState(pos.add(dx, 0, dz), altarStructure[dx + 31][dz + 31].getDefaultState());
						}
					}
					return true;
				}
			} else if (stack.getItem() instanceof ILoli || stack.getItem() instanceof ItemSmallLoliPickaxe) {
				for (int dx = -31; dx <= 31; dx++) {
					for (int dz = -31; dz <= 31; dz++) {
						if (worldIn.getBlockState(pos.add(dx, 0, dz)).getBlock() != altarStructure[dx + 31][dz + 31]) {
							return true;
						}
					}
				}
				for (int dx = -31; dx <= 31; dx++) {
					for (int dz = -31; dz <= 31; dz++) {
						worldIn.setBlockToAir(pos.add(dx, 0, dz));
					}
				}
				EntityLoli loli = new EntityLoli(worldIn);
				loli.setPosition(pos.getX() + 0.5, pos.getY(), pos.getZ() + 0.5);
				worldIn.spawnEntity(loli);
				return true;
			}
		}
		return true;
	}

	@Override
	@SideOnly(Side.CLIENT)
	public void addInformation(ItemStack stack, World player, List<String> tooltip, ITooltipFlag advanced) {
		tooltip.add(I18n.format("loliAltar.use"));
	}

}
