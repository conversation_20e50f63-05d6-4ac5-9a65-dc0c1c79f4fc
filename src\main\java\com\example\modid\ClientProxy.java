package com.example.modid;

import net.minecraft.client.renderer.block.model.ModelResourceLocation;
import net.minecraft.item.Item;
import net.minecraftforge.client.event.ModelRegistryEvent;
import net.minecraftforge.client.model.ModelLoader;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.relauncher.Side;

@Mod.EventBusSubscriber(value = Side.CLIENT, modid = Tags.MOD_ID)
public class ClientProxy {
    
    @SubscribeEvent
    public static void registerModels(ModelRegistryEvent event) {
        registerItemModel(ModItems.TIANSHI_SWORD, "tianshi_sword");
    }
    
    private static void registerItemModel(Item item, String name) {
        ModelLoader.setCustomModelResourceLocation(item, 0, 
            new ModelResourceLocation(Tags.MOD_ID + ":" + name, "inventory"));
    }
}
